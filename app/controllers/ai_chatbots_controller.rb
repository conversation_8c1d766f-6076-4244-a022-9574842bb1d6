class AiChatbotsController < ApplicationController
  def create
    messages = chatbot_params[:messages] || []

    chatbot = CarrierNetworkBuilders::ShipperProspectingChatbot.new(messages)
    response = chatbot.call

    render json: response
  rescue OpenAI::Api::Error
    render json: {
      type: 'error',
      content: 'I apologize, but I\'m having trouble processing your request right now. Please try again.'
    }, status: :service_unavailable
  rescue StandardError => e
    Rails.logger.error "AI Chatbot Error: #{e.message}"
    render json: {
      type: 'error',
      content: 'Something went wrong. Please try again.'
    }, status: :internal_server_error
  end

  private

  def chatbot_params
    params.permit(messages: %i(role content))
  end
end
