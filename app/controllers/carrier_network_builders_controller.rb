class CarrierNetworkBuildersController < ApplicationController
  before_action :require_login
  before_action :set_carrier_network_builder, only: %i(show edit update destroy)

  def index
    @carrier_network_builders = CarrierNetworkBuilder.where(user: current_user)
  end

  def show
    @lanes = @carrier_network_builder.lanes
    render layout: 'core'
  end

  def new
    @carrier_network_builder = CarrierNetworkBuilder.new(user: current_user)
  end

  def edit
  end

  def create
    @carrier_network_builder = CarrierNetworkBuilder.new(carrier_network_builder_params)
    @carrier_network_builder.assign_attributes(user: current_user)

    if @carrier_network_builder.save
      redirect_to carrier_network_builder_url(@carrier_network_builder),
                  notice: 'Carrier network builder was successfully created.'
    else
      render :new, status: :unprocessable_entity
    end
  end

  def update
    if @carrier_network_builder.update(carrier_network_builder_params)
      redirect_to @carrier_network_builder, notice: 'Carrier network builder was successfully updated.'
    else
      render :edit, status: :unprocessable_entity
    end
  end

  def destroy
    @carrier_network_builder.destroy!
    redirect_to carrier_network_builders_url, notice: 'Carrier network builder was successfully deleted.'
  end

  private

  def set_carrier_network_builder
    @carrier_network_builder = CarrierNetworkBuilder.where(user: current_user).find_by(uuid: params[:id])
  end

  def carrier_network_builder_params
    params.expect(carrier_network_builder: [:name])
  end
end
