class LaneSearchesController < ApplicationController
  def index
    city_ids = ENV['LANE_SEARCH_CITY_IDS'].to_s.split(',').map(&:to_i)
    @cities = City.where(id: city_ids).order('random()').limit(6)
  end

  def show
    @origin = LaneSearches::City.new(params[:origin]).record
    @destination = LaneSearches::City.new(params[:destination]).record

    @search_form = Forms::LaneSearch.from_params(
      params, defaults: { origin: @origin.full_slug, destination: @destination.full_slug, entity_type: :carrier }
    )

    Companies::SearchProviders::Standard.new(@search_form, params).then do |provider|
      @provider = provider
      @carriers = provider.records
      @results = provider.results
    end
  end

  def create
    origin = City.find params[:origin]
    destination = City.find params[:destination]

    form = Forms::LaneSearch.from_params(
      params.except(:origin, :destination),
      defaults: { origin: origin.full_slug, destination: destination.full_slug },
      nested: false
    )

    redirect_to lane_search_url(origin: origin.full_slug, destination: destination.full_slug,
                                lane_search: form.to_h.except(:origin, :destination, :sort))
  end
end
