module Analytics
  module Events
    class LaneSearchCriteria < SearchCriteria
      def attributes
        super.merge(
          search_destination_city_id: destination_city.id,
          search_destination_state_id: destination_city.state.id,
          search_destination_region_id: destination_city.state.region.id,
          search_destination_country_id: destination_city.country.id
        ).compact_blank
      end

      def form
        @form ||= Rack::Utils.parse_nested_query(uri.query.to_s).then do |params|
          Forms::LaneSearch.from_params(params)
        end
      end

      def city
        @city ||= LaneSearches::City.new(path_parameters['origin']).record
      end

      def destination_city
        @destination_city ||= LaneSearches::City.new(path_parameters['destination']).record
      end
    end
  end
end
