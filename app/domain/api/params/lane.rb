module Api
  module Params
    class Lane < Base
      include ActiveModel::Validations

      transform_types { |t| t.required(false) }

      attribute :origin, Types.Instance(::City).optional
      attribute :destination, Types.Instance(::City).optional

      attribute(:filter, CarrierFilter.default { CarrierFilter.new })

      validates :origin, :destination, presence: true

      def to_es_query
        { filters: filter.to_es_filters, shoulds: to_es_shoulds, order: [:cs_score] }
      end

      def to_es_shoulds
        LaneSearches::QueryFilters.new(
          origin: origin.full_slug, destination: destination.full_slug
        ).options.then do |options|
          {
            preferred_lanes: options,
            review_lanes: options,
            operation_states: options
          }
        end
      end

      def url_attributes
        {
          origin: origin.full_slug,
          destination: destination.full_slug,
          lane_search: filter.attributes.compact_blank
        }.compact_blank
      end
    end
  end
end
