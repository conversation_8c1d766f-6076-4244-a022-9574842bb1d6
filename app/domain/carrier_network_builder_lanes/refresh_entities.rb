module CarrierNetworkBuilderLanes
  class RefreshEntities
    include Callable

    attr_reader :carrier_network_builder_lane

    delegate :filters_form, to: :carrier_network_builder_lane

    def initialize(carrier_network_builder_lane)
      @carrier_network_builder_lane = carrier_network_builder_lane
    end

    def call
      CarrierNetworkBuilderLaneEntity.transaction do
        CarrierNetworkBuilderLaneEntity.where(carrier_network_builder_lane:).delete_all
        CarrierNetworkBuilderLaneEntity.insert_all!(attributes)
      end
    end

    private

    def records
      Companies::SearchProviders::Standard.new(filters_form, {}, max_per_page: 5).records(includes: [])
    end

    def attributes
      records.each_with_index.map do |record, index|
        {
          carrier_network_builder_lane_id: carrier_network_builder_lane.id,
          company_id: record.id,
          entity_type: record.entity_type,
          row_order: index
        }
      end
    end
  end
end
