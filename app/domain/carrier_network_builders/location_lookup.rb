module CarrierNetworkBuilders
  class LocationLookup
    include Callable

    attr_reader :location_string, :type

    def initialize(location_string, type = nil)
      @location_string = location_string.to_s.strip
      @type = type
    end

    def call
      return nil if location_string.blank?

      case type&.downcase
      when 'city'
        find_city
      when 'state'
        find_state
      when 'region'
        find_region
      else
        find_city || find_state || find_region
      end
    end

    private

    def find_city
      # Try exact city search first
      city = search_cities_by_name.first
      return city if city

      # Try parsing "City, State" format
      if location_string.include?(',')
        city_name, state_part = location_string.split(',', 2).map(&:strip)
        state = find_state_by_name(state_part)
        
        if state
          City.where(state_code: state.abbr)
              .where('LOWER(name) = ?', city_name.downcase)
              .first
        end
      end
    end

    def find_state
      find_state_by_name(location_string)
    end

    def find_region
      # For now, only support US regions
      us_country = Geo::Country.find('us')
      return nil unless us_country

      us_country.regions.find { |region| region.name.downcase == location_string.downcase }
    end

    def search_cities_by_name
      query = Cities::PrefixQuery.call(location_string)
      City.es.search(query).page(1).per(5).results.map(&:_source).map do |result|
        City.find(result['id'])
      end
    rescue
      []
    end

    def find_state_by_name(name)
      # For now, only support US states
      us_country = Geo::Country.find('us')
      return nil unless us_country

      # Try by full name first
      state = us_country.states.find { |s| s.name.downcase == name.downcase }
      return state if state

      # Try by abbreviation
      us_country.states.find { |s| s.abbr.downcase == name.downcase }
    end
  end
end
