module Elastic
  module Companies
    module Lanes
      extend Dry::Container::Mixin

      namespace :city do
        register :pickup do |prefix, options|
          geo_distance "#{prefix}.pickup_location" do
            distance "#{options.dig(:pickup, :radius)}mi"
            lat options.dig(:pickup, :lat)
            lon options.dig(:pickup, :lon)
          end
        end

        register :dropoff do |prefix, options|
          geo_distance "#{prefix}.dropoff_location" do
            distance "#{options.dig(:dropoff, :radius)}mi"
            lat options.dig(:dropoff, :lat)
            lon options.dig(:dropoff, :lon)
          end
        end
      end

      namespace :state do
        register :pickup do |prefix, options|
          term "#{prefix}.pickup_state_id" => options.dig(:pickup, :state_id)
        end

        register :dropoff do |prefix, options|
          term "#{prefix}.dropoff_state_id" => options.dig(:dropoff, :state_id)
        end
      end

      namespace :region do
        register :pickup do |prefix, options|
          term "#{prefix}.pickup_region_id" => options.dig(:pickup, :region_id)
        end

        register :dropoff do |prefix, options|
          term "#{prefix}.dropoff_region_id" => options.dig(:dropoff, :region_id)
        end
      end
    end
  end
end
