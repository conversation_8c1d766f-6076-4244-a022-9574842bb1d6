module Elastic
  module Companies
    class Shoulds
      extend Dry::Container::Mixin

      register :query_best_fields do |input|
        multi_match do
          query input.to_s[0..250]
          type 'best_fields'
          fields %w(docket_number.raw plain_docket_number.raw dot_number.raw name.raw)
        end
      end

      register :query_phrase_prefix do |input|
        multi_match do
          query input.to_s[0..250]
          type 'phrase_prefix'
          fields %w(docket_number plain_docket_number dot_number name.suggest)
        end
      end

      register :preferred_lanes do |options|
        nested do
          _name 'preferred_lanes'
          path :preferred_lanes
          query do
            bool do
              boost 3

              must do
                instance_exec(
                  :preferred_lanes,
                  options,
                  &Elastic::Companies::Lanes.resolve("#{options.dig(:pickup, :type)}.pickup")
                )
              end

              must do
                instance_exec(
                  :preferred_lanes,
                  options,
                  &Elastic::Companies::Lanes.resolve("#{options.dig(:dropoff, :type)}.dropoff")
                )
              end
            end
          end

          inner_hits _source: false, docvalue_fields: %w(preferred_lanes.pickup_city_id preferred_lanes.dropoff_city_id)
        end
      end

      register :review_lanes do |options|
        nested do
          _name 'review_lanes'
          path :review_lanes
          query do
            bool do
              boost 2

              must do
                instance_exec(
                  :review_lanes,
                  options,
                  &Elastic::Companies::Lanes.resolve("#{options.dig(:pickup, :type)}.pickup")
                )
              end

              must do
                instance_exec(
                  :review_lanes,
                  options,
                  &Elastic::Companies::Lanes.resolve("#{options.dig(:dropoff, :type)}.dropoff")
                )
              end
            end
          end

          inner_hits _source: false, docvalue_fields: %w(review_lanes.pickup_city_id review_lanes.dropoff_city_id)
        end
      end

      register :operation_states do |options|
        bool do
          _name 'operation_states'
          must { terms operation_states: Array.wrap(options.dig(:pickup, :state_id)) }
          must { terms operation_states: Array.wrap(options.dig(:dropoff, :state_id)) }
        end
      end

      register :address do |input|
        multi_match do
          query input
          type 'phrase'
          fields %w(physical_address mailing_address)
        end
      end

      register :phone do |value|
        term 'phone' => value
      end
    end
  end
end
