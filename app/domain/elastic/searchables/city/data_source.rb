module Elastic
  module Searchables
    module City
      class DataSource
        attr_reader :city

        delegate :path, :postal_codes, to: :city

        def initialize(city)
          @city = city
        end

        def as_indexed_json(_options)
          city.as_json(only: %i(id name state_code), methods: %i(full_slug))
            .merge('zipcodes' => zipcodes, 'zipcode_count' => zipcodes.size,
                   'city_and_state' => city_and_state, 'trucking_companies_path' => path.join('/'))
        end

        private

        def city_and_state
          [city.name, city.state_code].join(' ')
        end

        def zipcodes
          @zipcodes ||= postal_codes.pluck(:code)
        end
      end
    end
  end
end
