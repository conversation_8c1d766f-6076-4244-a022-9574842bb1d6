module Forms
  class LaneSearch < Forms::Base
    include CarrierAttributes

    attribute :origin, Types::String
    attribute :destination, Types::String
    attribute :sort, Types::Coercible::Symbol.default(:cs_score).enum(:cs_score, :rfp)

    def to_es_query
      { filters: to_es_filters, shoulds: to_es_shoulds, order: [sort] }
    end

    def to_es_shoulds
      LaneSearches::QueryFilters.new(origin:, destination:).options.then do |options|
        {
          preferred_lanes: options,
          review_lanes: options,
          operation_states: options
        }
      end
    end
  end
end
