module Geo
  class Region
    include Conifer
    include GlobalID::Identification

    conifer :regions, singleton: true

    ID_DELIMITER = ':'.freeze

    class << self
      def country(country)
        regions[country.id].map { |attrs| new(country:, **attrs.symbolize_keys) }
      end

      def where(id:)
        Array.wrap(id).filter_map { |ident| find(ident) }
      end

      def find(id)
        return if id.blank?

        country, region = id.split(ID_DELIMITER)
        country = Geo::Country.find(country)
        return if country.blank?

        country.regions.find_by(slug: region)
      end
    end

    attr_reader :country, :name, :slug

    def initialize(country:, name:, slug:)
      @country = country
      @name = name
      @slug = slug
    end

    def id
      [country.slug, slug].join(ID_DELIMITER)
    end

    def states
      @states ||= Geo::StateCollection.new(country:, states: Geo::State.region(country, self))
    end

    alias label name

    def inspect
      inspection = { country: country.alpha2, name:, slug: }.map { |k, v| "#{k}: #{v.inspect}" }.join(', ')
      "#<#{self.class} #{inspection}>"
    end
  end
end
