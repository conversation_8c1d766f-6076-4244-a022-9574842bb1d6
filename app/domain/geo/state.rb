module Geo
  class State
    include Conifer
    include GlobalID::Identification

    conifer :states, singleton: true

    ID_DELIMITER = ':'.freeze

    class << self
      def country(country)
        states[country.id].map { |attrs| new(country:, **attrs.symbolize_keys) }
      end

      def region(country, region)
        states[country.id]
          .select { |attrs| attrs['region'] == region.slug }
          .map { |attrs| new(country:, **attrs.symbolize_keys) }
      end

      def where(id:)
        Array.wrap(id).filter_map { |ident| find(ident) }
      end

      def find(id)
        return if id.blank?

        country, state = id.split(ID_DELIMITER)
        country = Geo::Country.find(country)
        return if country.blank?

        country.states.find_by(slug: state)
      end
    end

    attr_reader :country, :name, :abbr, :abbrs, :slug, :region

    # rubocop:disable Metrics/ParameterLists
    def initialize(country:, name:, abbr:, slug:, abbrs: [], region: nil)
      @country = country
      @name = name
      @abbr = abbr
      @abbrs = abbrs
      @slug = slug
      @region = country.regions.find_by(slug: region) || Nullable.object('geo/region')
    end
    # rubocop:enable Metrics/ParameterLists

    def id
      [country.slug, slug].join(ID_DELIMITER)
    end

    def path
      country.path.push(slug)
    end

    def label
      [name, country.alpha2].join(', ')
    end

    def inspect
      inspection = { country: country.alpha2, name:, abbr:, slug: }.map { |k, v| "#{k}: #{v.inspect}" }.join(', ')
      "#<#{self.class} #{inspection}>"
    end
  end
end
