module LaneSearches
  class City
    REGEX = /\A(?<state>[\w-]+:[\w-]+):(?<city>[\w-]+)\z/

    attr_reader :id

    def initialize(id)
      @id = id
    end

    def record(allow_nil: false)
      return unless match
      ::City.where(country_code: state.country.alpha2, state_code: state.abbr).friendly.find(match[:city], allow_nil:)
    end

    private

    def state
      @state ||= Geo::State.find(match[:state]) || Nullable.object('geo/state')
    end

    def match
      @match ||= REGEX.match(id)
    end
  end
end
