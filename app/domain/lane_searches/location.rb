module LaneSearches
  class Location
    TYPE_INFERENCE = proc do |id|
      case id.split(':')
      in [_, _, _]
        :city
      in [country, slug]
        Geo::Region.find("#{country}:#{slug}").present? ? :region : :state
      else
        raise ArgumentError, "Invalid ID format: #{id}"
      end
    end

    CityAdapter = Struct.new(:value) do
      def record
        LaneSearches::City.new(value).record
      end
    end

    StateAdapter = Struct.new(:value) do
      def record
        Geo::State.find(value)
      end
    end

    RegionAdapter = Struct.new(:value) do
      def record
        Geo::Region.find(value)
      end
    end

    attr_reader :id, :type

    def initialize(id)
      @id = id
      @type = TYPE_INFERENCE.call(id)
    end

    def record
      self.class.const_get("#{type.to_s.classify}Adapter", false).new(id).record
    end
  end
end
