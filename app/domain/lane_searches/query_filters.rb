module LaneSearches
  class QueryFilters
    CityAdapter = Struct.new(:record) do
      def options
        { radius: 50, state_id: record.state.id, region_id: record.state.region.id, **record.location }
      end
    end

    StateAdapter = Struct.new(:record) do
      def options
        { state_id: record.id, region_id: record.region.id }
      end
    end

    RegionAdapter = Struct.new(:record) do
      def options
        { state_id: record.states.map(&:id), region_id: record.id }
      end
    end

    attr_reader :origin, :destination

    def initialize(origin:, destination:)
      @origin = origin
      @destination = destination
    end

    def options
      {
        pickup: { type: origin_location.type, **adapter(origin_location).options },
        dropoff: { type: destination_location.type, **adapter(destination_location).options }
      }
    end

    private

    def origin_location
      @origin_location ||= LaneSearches::Location.new(origin)
    end

    def destination_location
      @destination_location ||= LaneSearches::Location.new(destination)
    end

    def adapter(location)
      self.class.const_get("#{location.type.to_s.classify}Adapter", false).new(location.record)
    end
  end
end
