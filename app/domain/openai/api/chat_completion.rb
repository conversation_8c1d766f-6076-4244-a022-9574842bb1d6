module OpenAI
  module Api
    class ChatCompletion
      include OpenAI::Api::Base

      def create(messages:, model: 'gpt-4o-mini', temperature: 0.7, max_tokens: 1000, response_format: nil)
        params = {
          model: model,
          messages: messages,
          temperature: temperature,
          max_tokens: max_tokens
        }
        
        params[:response_format] = response_format if response_format
        
        post('/chat/completions', json: params)
      end
    end
  end
end
