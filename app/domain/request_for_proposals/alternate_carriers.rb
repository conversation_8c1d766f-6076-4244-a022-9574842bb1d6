module RequestForProposals
  class AlternateCarriers
    include Callable

    attr_reader :request_for_proposal

    delegate :company_id, :pickup_city, :dropoff_city, :freight_id, :shipment_type_id, :specialized_service_id,
             :truck_type_id, to: :request_for_proposal

    def initialize(request_for_proposal)
      @request_for_proposal = request_for_proposal
    end

    def call
      ::Companies::ElasticQuery.new(query, { max: 5 }, entity_types: :carrier)
        .records(includes: [:reviews_aggregate, { profile: :users }])
    end

    private

    def query
      Elastic::Companies::QueryBuilder.new(must_nots: { id: company_id }, **form.to_es_query).build
    end

    def form
      Forms::LaneSearch.new(
        origin: pickup_city.full_slug, destination: dropoff_city.full_slug, authorized: true, claimed: true,
        freight: freight_id, shipment_type: shipment_type_id, specialized_service: specialized_service_id,
        truck_type: truck_type_id, star_rating_min: 3.5, sort: :rfp
      )
    end
  end
end
