import { Controller } from '@hotwired/stimulus'
import { useApplication } from 'stimulus-use'

export default class extends Controller {
  static targets = ['input', 'messages', 'sendButton']
  static values = {
    endpoint: String,
    carrierNetworkBuilderId: String
  }

  connect() {
    useApplication(this)
    this.messages = []
    this.isLoading = false

    // Focus the input field
    if (this.hasInputTarget) {
      this.inputTarget.focus()
    }
  }

  async sendMessage(event) {
    event.preventDefault()

    if (this.isLoading) return

    const message = this.inputTarget.value.trim()
    if (!message) return

    // Add user message to chat
    this.addMessage('user', message)
    this.inputTarget.value = ''
    this.setLoading(true)

    try {
      // Send to AI chatbot
      const response = await this.callChatbot(message)

      if (response.type === 'message') {
        // Add AI response to chat
        this.addMessage('assistant', response.content)
      } else if (response.type === 'requirements') {
        // Show success message and create lane
        this.addMessage(
          'assistant',
          'Great! I found your shipping requirements. Let me create a lane for you.'
        )
        await this.handleRequirements(response)
      } else if (response.type === 'error') {
        // Show error message
        this.addMessage('assistant', response.content)
      }
    } catch (error) {
      console.error('Chat error:', error)
      this.addMessage(
        'assistant',
        "I apologize, but I'm having trouble processing your request right now. Please try again."
      )
    } finally {
      this.setLoading(false)
    }
  }

  async callChatbot(userMessage) {
    // Add user message to conversation history
    this.messages.push({ role: 'user', content: userMessage })

    const response = await fetch(this.endpointValue, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-Token': this.csrfToken
      },
      body: JSON.stringify({
        messages: this.messages
      })
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}`)
    }

    const data = await response.json()

    // Add assistant response to conversation history (if not an error)
    if (data.type === 'message') {
      this.messages.push({ role: 'assistant', content: data.content })
    }

    return data
  }

  addMessage(role, content) {
    const messageDiv = document.createElement('div')
    messageDiv.className = 'flex gap-3 mb-4'

    if (role === 'assistant') {
      messageDiv.innerHTML = `
        <div class="flex h-8 w-8 flex-none items-center justify-center rounded-full fancy-border-outline">
          ${this.getLogoSvg()}
        </div>
        <div class="mr-12 rounded-lg to-purple-100 p-3 bg-linear-90 from-primary-100">
          <p>${this.escapeHtml(content)}</p>
        </div>
      `
    } else {
      messageDiv.innerHTML = `
        <div class="flex-auto"></div>
        <div class="ml-12 rounded-lg bg-gray-100 p-3 max-w-[80%]">
          ${this.escapeHtml(content)}
        </div>
      `
    }

    this.messagesTarget.appendChild(messageDiv)
    this.scrollToBottom()
  }

  async handleRequirements(response) {
    console.log('Requirements collected:', response)

    try {
      // Create the lane using the existing controller action
      const createResponse = await this.createLane(response)

      if (createResponse.ok) {
        // Process the turbo stream response
        const turboStreamContent = await createResponse.text()

        // Let Turbo handle the stream response (this will update the UI)
        if (window.Turbo) {
          window.Turbo.renderStreamMessage(turboStreamContent)
        }

        // Show success message
        this.addMessage(
          'assistant',
          "Perfect! I've created a new lane for you. The lane has been added to your list."
        )
      } else {
        // Handle error response
        this.addMessage(
          'assistant',
          'I was able to collect your requirements, but there was an issue creating the lane. Please try again or create it manually.'
        )
        console.error('Lane creation failed:', createResponse.status, createResponse.statusText)
      }
    } catch (error) {
      console.error('Error creating lane:', error)
      this.addMessage(
        'assistant',
        'I collected your requirements successfully, but encountered an error while creating the lane. Please try creating it manually.'
      )
    }
  }

  async createLane(chatbotResponse) {
    // Build the lane parameters from the chatbot response
    const laneParams = this.buildLaneParams(chatbotResponse)

    return await fetch(
      `/carrier_network_builders/${this.carrierNetworkBuilderIdValue}/lanes`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'text/vnd.turbo-stream.html',
          'X-CSRF-Token': this.csrfToken
        },
        body: JSON.stringify({
          carrier_network_builder_lane: laneParams
        })
      }
    )
  }

  buildLaneParams(chatbotResponse) {
    const { data } = chatbotResponse

    // Extract filter fields using a pick-like approach
    const filterFields = [
      'origin',
      'destination',
      'truck_type',
      'shipment_type',
      'specialized_service',
      'freight'
    ]
    const filters = Object.fromEntries(
      filterFields
        .filter(
          (field) =>
            data[field] &&
            (!Array.isArray(data[field]) || data[field].length > 0)
        )
        .map((field) => [field, data[field]])
    )

    return {
      volume: data.volume || null,
      frequency: data.frequency || null,
      filters
    }
  }

  setLoading(loading) {
    this.isLoading = loading

    if (loading) {
      // Add typing indicator
      const typingDiv = document.createElement('div')
      typingDiv.className = 'flex gap-3 mb-4'
      typingDiv.id = 'typing-indicator'
      typingDiv.innerHTML = `
        <div class="flex h-8 w-8 flex-none items-center justify-center rounded-full fancy-border-outline">
          ${this.getLogoSvg()}
        </div>
        <div class="mr-12 rounded-lg to-purple-100 p-3 bg-linear-90 from-primary-100">
          <p class="text-gray-600">Typing...</p>
        </div>
      `
      this.messagesTarget.appendChild(typingDiv)
      this.scrollToBottom()
    } else {
      // Remove typing indicator
      const typingIndicator = document.getElementById('typing-indicator')
      if (typingIndicator) {
        typingIndicator.remove()
      }
    }

    // Disable/enable input and send button
    this.inputTarget.disabled = loading
    if (this.hasSendButtonTarget) {
      this.sendButtonTarget.disabled = loading
      this.sendButtonTarget.classList.toggle('opacity-50', loading)
    }
  }

  scrollToBottom() {
    this.messagesTarget.scrollTop = this.messagesTarget.scrollHeight
  }

  escapeHtml(text) {
    const div = document.createElement('div')
    div.textContent = text
    return div.innerHTML
  }

  getLogoSvg() {
    // Get the logo SVG from the first message to ensure consistency
    const firstMessageLogo = document.querySelector('.fancy-border-outline svg')
    if (firstMessageLogo) {
      return firstMessageLogo.outerHTML
    }

    // Fallback SVG if the first message logo isn't found
    return `<svg class="h-7 w-7 fill-primary -ml-px" viewBox="0 0 24 24">
      <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
    </svg>`
  }

  // Handle Enter key in input
  handleKeydown(event) {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault()
      this.sendMessage(event)
    }
  }
}
