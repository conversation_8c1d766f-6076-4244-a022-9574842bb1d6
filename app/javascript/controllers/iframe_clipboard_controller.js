import { Controller } from '@hotwired/stimulus'

export default class extends Controller {
  static targets = ['button']
  static values = {
    iframeId: String,
    sourceId: String,
    successContent: String,
    successDuration: {
      type: Number,
      default: 2000
    }
  }

  connect() {
    if (this.hasButtonTarget) {
      this.originalContent = this.buttonTarget.innerHTML
    }
  }

  async copy(e) {
    e.preventDefault()

    try {
      const iframe = document.getElementById(this.iframeIdValue)
      const iframeWindow = iframe.contentWindow
      const iframeDocument = iframe.contentDocument || iframeWindow.document
      const sourceTarget = iframeDocument.getElementById(this.sourceIdValue)

      const htmlContent = sourceTarget.outerHTML
      const plainTextContent =
        sourceTarget.innerText || sourceTarget.textContent

      const clipboardItem = new ClipboardItem({
        'text/html': new Blob([htmlContent], { type: 'text/html' }),
        'text/plain': new Blob([plainTextContent], { type: 'text/plain' })
      })

      await navigator.clipboard.write([clipboardItem])
      this.copied()
    } catch (error) {
      console.error('Failed to copy to clipboard:', error)
    }
  }

  copied() {
    if (this.hasButtonTarget) {
      if (this.timeout) {
        clearTimeout(this.timeout)
      }

      this.buttonTarget.innerHTML = this.successContentValue

      this.timeout = setTimeout(() => {
        this.buttonTarget.innerHTML = this.originalContent
      }, this.successDurationValue)
    }
  }
}
