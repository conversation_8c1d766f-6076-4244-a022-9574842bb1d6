# == Schema Information
#
# Table name: carrier_network_builder_lanes
#
#  id                         :bigint           not null, primary key
#  filters                    :jsonb
#  frequency                  :string
#  notes                      :text
#  volume                     :string
#  created_at                 :datetime         not null
#  updated_at                 :datetime         not null
#  carrier_network_builder_id :bigint           not null
#
# Indexes
#
#  idx_on_carrier_network_builder_id_4173102239  (carrier_network_builder_id)
#
# Foreign Keys
#
#  fk_rails_60ac8dd0e2  (carrier_network_builder_id => carrier_network_builders.id) ON DELETE => cascade
#
class CarrierNetworkBuilderLane < ApplicationRecord
  belongs_to :carrier_network_builder

  has_many :entities, class_name: 'CarrierNetworkBuilderLaneEntity', dependent: :destroy

  after_commit on: %i(create update) do
    CarrierNetworkBuilderLanes::RefreshEntitiesJob.perform_async(id)
  end

  def origin_id
    filters['origin']
  end

  def destination_id
    filters['destination']
  end

  def origin
    return Nullable.object(:city) if origin_id.blank?
    LaneSearches::Location.new(origin_id).record
  end

  def destination
    return Nullable.object(:city) if destination_id.blank?
    LaneSearches::Location.new(destination_id).record
  end

  def filters_form
    Forms::LaneSearch.new(filters)
  end

  def origin_type
    return if origin_id.blank?
    LaneSearches::Location.new(origin_id).type.to_s
  end

  def destination_type
    return if destination_id.blank?
    LaneSearches::Location.new(destination_id).type.to_s
  end
end
