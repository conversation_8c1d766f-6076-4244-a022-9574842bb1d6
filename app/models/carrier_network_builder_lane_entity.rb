# == Schema Information
#
# Table name: carrier_network_builder_lane_entities
#
#  id                              :bigint           not null, primary key
#  entity_type                     :string           not null
#  row_order                       :integer
#  created_at                      :datetime         not null
#  updated_at                      :datetime         not null
#  carrier_network_builder_lane_id :bigint           not null
#  company_id                      :bigint           not null
#
# Indexes
#
#  index_carrier_network_builder_lane_entities_on_company_id       (company_id)
#  index_carrier_network_builder_lane_entities_on_lane_and_entity  (carrier_network_builder_lane_id,company_id,entity_type) UNIQUE
#
# Foreign Keys
#
#  fk_rails_652d9c4044  (company_id => companies.id) ON DELETE => cascade
#  fk_rails_d180ba59d4  (carrier_network_builder_lane_id => carrier_network_builder_lanes.id) ON DELETE => cascade
#
class CarrierNetworkBuilderLaneEntity < ApplicationRecord
  include RankedModel
  ranks :row_order, with_same: :carrier_network_builder_lane_id

  belongs_to :carrier_network_builder_lane
  belongs_to :company

  def entity
    company.as_entity(entity_type)
  end
end
