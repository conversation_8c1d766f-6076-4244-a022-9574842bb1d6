<div id="<%= dom_id(lane) %>" class="group" data-controller="dataset">
  <div class="flex items-center border-b border-gray-200 px-4 py-2 group-data-[open]:border-b-0">
    <div class="flex flex-auto cursor-pointer items-center gap-1" data-action="click->dataset#toggle">
                <span class="mr-2 -mt-0.75">
                  <%= svg_tag 'triangle-solid',
                              class: 'fill-gray-500 inline-block h-1.5 w-3 rotate-90 group-data-[open]:rotate-180',
                              preserveAspectRatio: 'none' %>
                </span>
      <span><%= lane.origin.label %></span>
      <%= svg_tag 'chevron-right-solid', class: 'fill-primary inline-block h-4 w-4' %>
      <span><%= lane.destination.label %></span>
    </div>

    <div class="flex flex-none items-center gap-3">
      <div data-controller="dropdown" data-action="click->dropdown#toggle click@window->dropdown#hide"
           class="relative cursor-pointer rounded-full p-3 hover:bg-primary-100">
        <%= svg_tag 'ellipsis-vertical-regular', class: 'h-4 w-4 fill-gray-600' %>

        <div data-dropdown-target="menu"
             data-transition-enter-from="opacity-0 scale-95"
             data-transition-enter-to="opacity-100 scale-100"
             data-transition-leave-from="opacity-100 scale-100"
             data-transition-leave-to="opacity-0 scale-95"
             class="hidden absolute top-0 transition transform right-[40px]">
          <div class="flex flex-col rounded border border-gray-300 bg-white divide-y divide-gray-300">
            <%= link_to edit_carrier_network_builder_lane_path(carrier_network_builder, lane),
                        data: { turbo_frame: 'modal' },
                        class: 'flex gap-2 px-3 py-2 hover:bg-primary-100 items-center' do %>
              <%= svg_tag 'pen-to-square-regular', class: 'h-4 w-4 fill-gray-600' %>
              <span>Edit</span>
            <% end %>
            <%= link_to carrier_network_builder_lane_path(carrier_network_builder, lane),
                        data: { turbo_method: :delete, turbo_confirm: 'Are you sure?' },
                        class: 'flex gap-2 px-3 py-2 hover:bg-primary-100 items-center' do %>
              <%= svg_tag 'trash-regular', class: 'h-4 w-4 fill-gray-600' %>
              <span>Delete</span>
            <% end %>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="mr-4 mb-4 ml-10 hidden rounded-xl border border-gray-300 group-data-[open]:block">
    <div class="flex gap-6 rounded-t-xl p-4 bg-primary-50">
      <% if lane.volume.present? %>
        <dl>
          <dt class="truncate text-xs text-gray-500">Volume</dt>
          <dd class="truncate text-sm"><%= lane.volume %></dd>
        </dl>
      <% end %>
      <% if lane.filters_form.shipment_type.present? %>
        <dl>
          <dt class="truncate text-xs text-gray-500">Shipment Type</dt>
          <% lane.filters_form.shipment_type.each do |st| %>
            <dd class="truncate text-sm"><%= Records[:shipment_types][st].name %></dd>
          <% end %>
        </dl>
      <% end %>
      <% if lane.filters_form.freight.present? %>
        <dl>
          <dt class="truncate text-xs text-gray-500">Freight Type</dt>
          <% lane.filters_form.freight.each do |f| %>
            <dd class="truncate text-sm"><%= Records[:freights][f].name %></dd>
          <% end %>
        </dl>
      <% end %>
      <% if lane.filters_form.truck_type.present? %>
        <dl>
          <dt class="truncate text-xs text-gray-500">Equipment</dt>
          <% lane.filters_form.truck_type.each do |tt| %>
            <dd class="truncate text-sm"><%= Records[:truck_types][tt].name %></dd>
          <% end %>
        </dl>
      <% end %>
      <% if lane.filters_form.specialized_service.present? %>
        <dl>
          <dt class="truncate text-xs text-gray-500">Specialized Service</dt>
          <% lane.filters_form.specialized_service.each do |ss| %>
            <dd class="truncate text-sm"><%= Records[:specialized_services][ss].name %></dd>
          <% end %>
        </dl>
      <% end %>
      <% if lane.notes.present? %>
        <dl>
          <dt class="truncate text-xs text-gray-500">Notes</dt>
          <dd class="text-sm"><%= lane.notes %></dd>
        </dl>
      <% end %>
    </div>

    <h4 class="mt-4 flex px-4 text-sm font-semibold text-black">
                <span class="mr-2">
                  <%= svg_tag 'triangle-solid',
                              class: 'fill-gray-500 inline-block h-1.5 w-3 rotate-180',
                              preserveAspectRatio: 'none' %>
                </span>
      Recommended Carriers
    </h4>

    <div class="m-4 ml-9 grid items-center border-x border-gray-200 text-xs border-t border-gray-200
                grid-cols-[min-content_min-content_auto_1fr_min-content_min-content]">
      <% lane.entities.order(:id).map { |le| Companies::EntityClassRegistry.lookup(entity: le.entity, type: :view_model).new(le.entity) }.each do |model| %>
        <div class="group/item contents divide-y divide-gray-200" data-controller="dataset">
          <div class="flex items-center px-3 h-18" data-action="click->dataset#toggle">
            <%= svg_tag 'triangle-solid',
                        class: 'fill-gray-500 inline-block h-1.5 w-3 rotate-90 group-data-[open]/item:rotate-180 cursor-pointer',
                        preserveAspectRatio: 'none' %>
          </div>

          <% if model.entity_type == :carrier %>
            <div class="flex items-center border-x border-gray-200 px-3 bg-primary-50 h-18">
              <%= svg_tag 'truck-regular', class: 'fill-primary inline-block h-4 w-4' %>
            </div>
          <% else %>
            <div class="flex items-center border-x border-gray-200 px-3 bg-purple-50 h-18">
              <%= svg_tag 'circle-nodes-regular', class: 'fill-purple inline-block h-4 w-4' %>
            </div>
          <% end %>

          <div class="flex flex-col justify-center px-3 h-18">
            <h4 class="text-black truncate"><%= model.name %></h4>
            <h5>USDOT: <%= model.dot_number %></h5>
          </div>

          <div class="flex items-center whitespace-nowrap px-3 h-18">
            <div class="-mr-4 -ml-6 scale-60">
              <%= render StarsComponent.new(rating: model.star_rating, class: 'fill-secondary') %>
            </div>
            <div><strong><%= model.star_rating %></strong> (<%= model.review_count %>)</div>
          </div>

          <div class="flex flex-col items-center justify-center px-3 h-18">
            <div><%= model.power_units %></div>
            <div class="text-2xs"><%= 'truck'.pluralize(model.power_units) %></div>
          </div>

          <div class="flex cursor-pointer items-center border-l border-gray-200 px-3 h-18 hover:bg-primary-50">
            <%= link_to carrier_contact_url(model.carrier), rel: 'noindex nofollow', data: { turbo_frame: 'modal' } do %>
              <%= svg_tag 'phone-regular', class: 'h-4 w-4 fill-gray-600' %>
            <% end %>
          </div>

          <!-- Expanded Row -->
          <div class="h-full hidden group-data-[open]/item:block"></div>
          <div class="hidden group-data-[open]/item:flex col-span-5 flex-col border-b border-gray-200 bg-white py-4 pr-4">
            <div class="flex gap-6">
              <dl class="flex w-1/2 flex-col gap-1">
                <div class="flex items-center gap-1">
                  <dt class="font-semibold text-black">Fleet Size</dt>
                  <dd><%= pluralize(model.power_units, 'truck') %></dd>
                </div>
                <div class="flex items-center gap-1">
                  <dt class="font-semibold text-black">MCS-150 Mileage</dt>
                  <dd><%= number_with_delimiter(model.mcs_150_mileage.to_i) %></dd>
                </div>
              </dl>

              <dl class="flex w-1/2 flex-col gap-1">
                <div class="flex items-center gap-1">
                  <dt class="font-semibold text-black">Authority</dt>
                  <dd>
                    <%= model.authority.title %> <%= model.authority.date ? "(#{l(model.authority.date, format: :mdy)})" : nil %>
                  </dd>
                </div>
                <div class="flex items-center gap-1">
                  <dt class="font-semibold text-black">Insurance</dt>
                  <dd><%= model.insurance_limit %></dd>
                </div>
                <div class="flex items-center gap-1">
                  <dt class="font-semibold text-black">Safety</dt>
                  <dd><%= model.safety_rating %></dd>
                </div>
              </dl>
            </div>

            <div class="mt-3 flex gap-6 border-t border-gray-200 pt-3">
              <% %w(freights truck_types shipment_types specialized_services).each do |method| %>
                <% services = model.public_send(method) %>
                <% if services.present? %>
                  <dl class="w-1/4">
                    <dt class="font-semibold text-black"><%= t("helpers.label.carrier.#{method}") %></dt>
                    <% services.each do |item| %>
                      <dd><%= item.name %></dd>
                    <% end %>
                  </dl>
                <% end %>
              <% end %>
            </div>
          </div>
        </div>
      <% end %>
    </div>
  </div>
</div>
