<% content_for :title, "<PERSON> Details - #{@carrier_network_builder.name}" %>

<div class="flex justify-between items-center mb-6">
  <div>
    <h1 class="text-2xl font-bold text-gray-900">Lane Details</h1>
    <p class="text-sm text-gray-500 mt-1">
      Lane in "<%= @carrier_network_builder.name %>"
    </p>
  </div>
  <div class="flex items-center space-x-3">
    <%= link_to 'Edit Lane', edit_carrier_network_builder_lane_path(@carrier_network_builder, @lane),
                class: 'btn secondary' %>
    <%= link_to 'Back to Lanes', carrier_network_builder_lanes_path(@carrier_network_builder),
                class: 'btn secondary' %>
  </div>
</div>

<div class="bg-white shadow sm:rounded-lg">
  <div class="px-4 py-5 sm:p-6">
    <div class="grid grid-cols-1 gap-6 lg:grid-cols-2">
      <!-- Origin Information -->
      <div class="space-y-4">
        <h3 class="text-lg leading-6 font-medium text-gray-900">Origin</h3>

        <div class="bg-gray-50 rounded-lg p-4">
          <div class="flex items-center mb-2">
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 mr-2">
              <%= @lane.origin_type.capitalize %>
            </span>
            <span class="text-sm font-medium text-gray-900">Type</span>
          </div>

          <div class="mt-3">
            <dt class="text-sm font-medium text-gray-500">Location</dt>
            <dd class="mt-1 text-sm text-gray-900">
              <%= @lane.origin.label %>
            </dd>
          </div>

          <% if @lane.origin_id.present? %>
            <div class="mt-3">
              <dt class="text-sm font-medium text-gray-500">ID</dt>
              <dd class="mt-1 text-sm text-gray-900 font-mono">
                <%= @lane.origin_id %>
              </dd>
            </div>
          <% end %>
        </div>
      </div>

      <!-- Destination Information -->
      <div class="space-y-4">
        <h3 class="text-lg leading-6 font-medium text-gray-900">Destination</h3>

        <div class="bg-gray-50 rounded-lg p-4">
          <div class="flex items-center mb-2">
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 mr-2">
              <%= @lane.destination_type.capitalize %>
            </span>
            <span class="text-sm font-medium text-gray-900">Type</span>
          </div>

          <div class="mt-3">
            <dt class="text-sm font-medium text-gray-500">Location</dt>
            <dd class="mt-1 text-sm text-gray-900">
              <%= @lane.destination.label %>
            </dd>
          </div>

          <% if @lane.destination_id.present? %>
            <div class="mt-3">
              <dt class="text-sm font-medium text-gray-500">ID</dt>
              <dd class="mt-1 text-sm text-gray-900 font-mono">
                <%= @lane.destination_id %>
              </dd>
            </div>
          <% end %>
        </div>
      </div>
    </div>

    <%= render HorizontalRuleComponent.new %>

    <!-- Lane Details -->
    <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
      <div>
        <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Lane Information</h3>

        <dl class="space-y-4">
          <div>
            <dt class="text-sm font-medium text-gray-500">Volume</dt>
            <dd class="mt-1 text-sm text-gray-900">
              <%= @lane.volume.presence || 'Not specified' %>
            </dd>
          </div>

          <div>
            <dt class="text-sm font-medium text-gray-500">Frequency</dt>
            <dd class="mt-1 text-sm text-gray-900">
              <%= @lane.frequency.presence || 'Not specified' %>
            </dd>
          </div>
        </dl>
      </div>

      <div>
        <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Additional Information</h3>

        <dl class="space-y-4">
          <div>
            <dt class="text-sm font-medium text-gray-500">Created</dt>
            <dd class="mt-1 text-sm text-gray-900">
              <%= time_ago_in_words(@lane.created_at) %> ago
            </dd>
          </div>

          <div>
            <dt class="text-sm font-medium text-gray-500">Last Updated</dt>
            <dd class="mt-1 text-sm text-gray-900">
              <%= time_ago_in_words(@lane.updated_at) %> ago
            </dd>
          </div>
        </dl>
      </div>
    </div>

    <% if @lane.notes.present? %>
      <%= render HorizontalRuleComponent.new %>

      <div>
        <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Notes</h3>
        <div class="bg-gray-50 rounded-lg p-4">
          <p class="text-sm text-gray-900 whitespace-pre-wrap"><%= @lane.notes %></p>
        </div>
      </div>
    <% end %>

    <% if @lane.filters.present? && @lane.filters.any? %>
      <%= render HorizontalRuleComponent.new %>

      <div>
        <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Filters</h3>
        <div class="bg-gray-50 rounded-lg p-4">
          <dl class="space-y-2">
            <% @lane.filters.each do |key, value| %>
              <div class="flex justify-between">
                <dt class="text-sm font-medium text-gray-500"><%= key.humanize %></dt>
                <dd class="text-sm text-gray-900"><%= value %></dd>
              </div>
            <% end %>
          </dl>
        </div>
      </div>
    <% end %>
  </div>
</div>

<div class="mt-6 flex justify-between">
  <div class="flex space-x-3">
    <%= link_to 'Back to Network Builder', @carrier_network_builder,
                class: 'text-gray-600 hover:text-gray-900 text-sm font-medium' %>
    <%= link_to 'All Lanes', carrier_network_builder_lanes_path(@carrier_network_builder),
                class: 'text-gray-600 hover:text-gray-900 text-sm font-medium' %>
  </div>

  <div class="flex space-x-3">
    <%= link_to 'Edit Lane', edit_carrier_network_builder_lane_path(@carrier_network_builder, @lane),
                class: 'btn secondary' %>
    <%= link_to 'Delete Lane', carrier_network_builder_lane_path(@carrier_network_builder, @lane),
                method: :delete,
                data: { confirm: 'Are you sure? This action cannot be undone.' },
                class: 'btn danger' %>
  </div>
</div>
