<%= turbo_stream.update 'app-notifications' do %>
  <%= render NotificationComponent.new(type: :notice) do |c| %>
    <%= c.with_title { 'Lane updated successfully' } %>
  <% end %>
<% end %>

<%= turbo_stream.update 'modal' %>

<%= turbo_stream.replace dom_id(@lane) do %>
  <%= render 'carrier_network_builder_lanes/lane',
             lane: @lane, carrier_network_builder: @carrier_network_builder %>
<% end %>
