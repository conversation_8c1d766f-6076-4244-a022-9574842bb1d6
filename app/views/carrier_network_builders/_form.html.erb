<%= form_with model: carrier_network_builder, 
              builder: ApplicationFormBuilder,
              data: { controller: 'forms--validation forms--unload' },
              class: 'space-y-6' do |form| %>

  <% if carrier_network_builder.errors.any? %>
    <div class="rounded-md bg-red-50 p-4">
      <div class="flex">
        <div class="flex-shrink-0">
          <%= svg_tag 'exclamation-circle-solid', class: 'h-5 w-5 text-red-400' %>
        </div>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-red-800">
            <%= pluralize(carrier_network_builder.errors.count, "error") %> prohibited this network builder from being saved:
          </h3>
          <div class="mt-2 text-sm text-red-700">
            <ul class="list-disc pl-5 space-y-1">
              <% carrier_network_builder.errors.full_messages.each do |message| %>
                <li><%= message %></li>
              <% end %>
            </ul>
          </div>
        </div>
      </div>
    </div>
  <% end %>

  <div class="bg-white shadow sm:rounded-lg">
    <div class="px-4 py-5 sm:p-6">
      <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
        Network Builder Details
      </h3>

      <div class="error-container">
        <%= form.label :name, class: 'block text-sm font-medium text-gray-700 mb-1' %>
        <%= form.text_field :name, 
                            required: true,
                            placeholder: 'Enter a name for this network builder',
                            class: 'mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary sm:text-sm' %>
      </div>
    </div>
  </div>

  <div class="flex justify-end space-x-3">
    <%= link_to 'Cancel', 
                carrier_network_builder.persisted? ? carrier_network_builder : carrier_network_builders_path, 
                class: 'btn secondary' %>
    <%= form.submit class: 'btn primary' %>
  </div>
<% end %>
