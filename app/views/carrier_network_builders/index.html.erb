<% content_for :title, 'Carrier Network Builders' %>

<div class="flex justify-between items-center mb-6">
  <h1 class="text-2xl font-bold text-gray-900">Carrier Network Builders</h1>
  <%= link_to 'New Network Builder', new_carrier_network_builder_path,
              class: 'btn primary' %>
</div>

<% if @carrier_network_builders.any? %>
  <div class="bg-white shadow overflow-hidden sm:rounded-md">
    <ul class="divide-y divide-gray-200">
      <% @carrier_network_builders.each do |builder| %>
        <li>
          <div class="px-4 py-4 flex items-center justify-between">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center">
                  <%= svg_tag 'truck-regular', class: 'h-5 w-5 text-primary' %>
                </div>
              </div>
              <div class="ml-4">
                <div class="flex items-center">
                  <%= link_to builder.name, carrier_network_builder_url(builder.uuid),
                              class: 'text-sm font-medium text-gray-900 hover:text-primary' %>
                  <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                    <%= pluralize(builder.lanes.count, 'lane') %>
                  </span>
                </div>
                <div class="text-sm text-gray-500">
                  Created <%= time_ago_in_words(builder.created_at) %> ago
                </div>
              </div>
            </div>
            <div class="flex items-center space-x-2">
              <%= link_to 'View', builder,
                          class: 'text-primary hover:text-primary-dark text-sm font-medium' %>
              <%= link_to 'Edit', edit_carrier_network_builder_path(builder),
                          class: 'text-gray-600 hover:text-gray-900 text-sm font-medium' %>
              <%= link_to 'Delete', builder, method: :delete,
                          data: { confirm: 'Are you sure?' },
                          class: 'text-red-600 hover:text-red-900 text-sm font-medium' %>
            </div>
          </div>
        </li>
      <% end %>
    </ul>
  </div>
<% else %>
  <div class="text-center py-12">
    <div class="mx-auto h-12 w-12 text-gray-400">
      <%= svg_tag 'truck-regular', class: 'h-12 w-12' %>
    </div>
    <h3 class="mt-2 text-sm font-medium text-gray-900">No network builders</h3>
    <p class="mt-1 text-sm text-gray-500">Get started by creating a new carrier network builder.</p>
    <div class="mt-6">
      <%= link_to 'New Network Builder', new_carrier_network_builder_path,
                  class: 'btn primary' %>
    </div>
  </div>
<% end %>
