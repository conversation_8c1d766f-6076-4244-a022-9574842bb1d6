<!DOCTYPE html>
<html lang="en">
  <body>
    <table cellpadding="0" cellspacing="0" id="email-signature"
           style="font-size: 14px; font-family: Arial, Helvetica, sans-serif; text-align: left; color: #333;">
      <tr>
        <% if @form.logo? %>
          <td style="vertical-align: middle; padding-right: 20px;">
            <%= image_tag @profile.logo, width: 100, height: 100, style: 'display: block;' %>
          </td>
        <% end %>
        <td style="vertical-align: middle; padding-right: 20px; ">
          <h3 style="font-size: 16px; font-weight: bold; color: #000; margin: 0 0 5px;">
            <%= @form.name %>
          </h3>
          <% if @form.title.present? %>
            <p style="margin: 0; padding-bottom: 2px;"><%= @form.title %></p>
          <% end %>
          <% if @form.company_name.present? %>
            <p style="margin: 0;"><%= @form.company_name %></p>
          <% end %>
        </td>
        <td style="width: 1px; background: #D1D5DB;"></td>
        <td style="padding-left: 20px;">
          <table cellpadding="0" cellspacing="0">
            <% if @form.phone.present? %>
              <tr>
                <td style="padding-right: 9px; vertical-align: top; padding-top:5px; padding-bottom: 5px;">
                  <%= external_image_tag 'phone-regular.png', height:"16", width:"16" %>
                </td>
                <td style="padding-top: 4px; vertical-align: top;"><%= @form.phone %></td>
              </tr>
            <% end %>
            <% if @form.email.present? %>
              <tr>
                <td style="padding-right: 9px; vertical-align: top; padding-top:5px; padding-bottom: 5px;">
                  <%= external_image_tag 'envelope-regular.png', height:"16", width:"16" %>
                </td>
                <td style="padding-top: 4px; vertical-align: top;">
                  <%= mail_to @form.email, style: 'color: #333; text-decoration:none;' %>
                </td>
              </tr>
            <% end %>
            <% if @form.website.present? %>
              <tr>
                <td style="padding-right: 9px; vertical-align: top; padding-top:5px; padding-bottom: 5px;">
                  <%= external_image_tag 'globe-regular.png', height:"16", width:"16" %>
                </td>
                <td style="padding-top: 4px; vertical-align: top;">
                  <a style="color: #333; text-decoration:none;" href="<%= @form.website %>">
                    <%= @form.website %>
                  </a>
                </td>
              </tr>
            <% end %>
            <% if @form.address1.present? || @form.address2.present? %>
              <tr>
                <td style="padding-right: 9px; vertical-align: top; padding-top:5px; padding-bottom: 5px;">
                  <%= external_image_tag 'location-dot-regular.png', height:"16", width:"16" %>
                </td>
                <td style="padding-top: 4px; vertical-align: top;">
                  <p style="margin: 0;"><%= @form.address1 %></p>
                  <p style="margin: 0; padding-top: 2px"><%= @form.address2 %></p>
                </td>
              </tr>
            <% end %>
          </table>
        </td>
      </tr>
      <tr>
        <td colspan="<%= @form.colspan %>" style="height: 20px;"></td>
      </tr>
      <tr>
        <td colspan="<%= @form.colspan %>" style="height: 5px;"></td>
      </tr>
      <tr>
        <td colspan="<%= @form.colspan %>" style="text-align: center">
          <%= link_to url_for([@profile.entity, utm_source: 'email_signature', utm_medium: 'email', utm_campaign: @profile.entity.slug]),
                      id: 'email-signature-image', target: '_blank' do %>
            <%= image_tag url_for([@profile.entity, :asset, asset_id: 'email_signature', format: :png, only_path: false]),
                          width: 200, height: 118, style: 'display: block; margin-left: auto; margin-right: auto;' %>
          <% end %>
        </td>
      </tr>
    </table>
  </body>
</html>
