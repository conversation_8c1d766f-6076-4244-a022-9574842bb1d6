<div data-controller="dropdown" data-action="click->dropdown#toggle click@window->dropdown#hide"
     class="relative py-1.5 px-2 hover:bg-gray-100 hover:cursor-pointer rounded-sm x-header-dropdown">
  <button class="flex flex-row items-center gap-1.5">
    <span class="flex flex-none justify-center items-center h-10 w-10 bg-primary rounded-full">
      <span class="text-white"><%= current_user.monogram %></span>
    </span>
    <%= svg_tag 'chevron-down-solid', class: 'w-3 h-3 fill-gray-600' %>
  </button>

  <div data-dropdown-target="menu"
       data-transition-enter-from="opacity-0 scale-95"
       data-transition-enter-to="opacity-100 scale-100"
       data-transition-leave-from="opacity-100 scale-100"
       data-transition-leave-to="opacity-0 scale-95"
       class="hidden transition transform origin-top-right absolute bg-white z-20 top-[52px] right-0 border
              border-gray-300 rounded-sm shadow-md">
    <ul>
      <% Users::Dropdown.items(current_user).each do |item| %>
        <li class="border-t border-gray-300 first:border-none">
          <a class="block whitespace-nowrap py-2 px-6 hover:bg-primary-50"
             href="<%= item.url %>" <%= tag.attributes(data: item.data) %>>
            <%= t("users.dropdown.#{item.i18n_key}") %>
          </a>
        </li>
      <% end %>
      <li class="border-t border-gray-300">
        <%= form_with url: sign_out_url, method: :delete do %>
          <button type="submit"
                  class='w-full inline-flex justify-start py-2 px-6 hover:bg-primary-50 x-btn-sign-out'>
            Sign out
          </button>
        <% end %>
      </li>
    </ul>
  </div>
</div>
