constraints Routes::BuildConstraints.call(subdomain: 'www') do
  # :nocov:
  mount Lookbook::Engine, at: '/lookbook' if Rails.env.development?
  # :nocov:

  mount Sidekiq::Web, at: '/sidekiq'

  root 'home#index'

  get '/search', to: 'search#show', as: :search
  get '/write-review', to: 'write_review#search', as: :write_review

  resource :ai_chatbot, only: :create

  match 'auth/:provider/callback', to: 'users#create_from_omniauth', via: %i(get post)
  get 'auth/failure', to: 'users#failure_from_omniauth'

  namespace :analytics do
    get 'integrations/oauth-redirect', to: 'integrations#oauth_redirect', as: :integrations_oauth_redirect

    resources :feeds do
      member do
        post :export
      end
    end

    resources :companies, only: [] do
      collection do
        get :search
      end
    end
  end

  resources :brokerages, only: :show do
    get '/assets/:asset_id', to: 'brokerages/assets#show', as: :asset

    collection do
      get '/write-review', to: 'brokerages/write_review#search', as: :write_review
      get :claim
    end

    member do
      get :review
    end

    resource :contact, only: %i(show), module: :brokerages
    resources :claims, only: %i(show new create), module: :brokerages
    resources :email_claims, only: %i(show update), module: :brokerages, path: 'email-claims', param: :token

    namespace :dashboard, module: 'brokerage_dashboard' do
      root to: 'overview/home#show'

      namespace :analytics do
        root to: 'home#show'

        resources :integrations, only: %i(index create)

        resources :feeds do
          resources :integrations, only: %i(index create edit update destroy), module: :feeds

          member do
            post :duplicate
            post :export
          end

          resources :shipper_intents, only: %i(index show) do
            collection do
              get :search
            end
          end
        end
      end

      resources :assets

      resource :profile, only: %i(show update) do
        resource :services, only: %i(show update), module: 'profile'
        resources :contacts, only: %i(index new create edit update destroy), module: 'profile' do
          member do
            patch :make_default
          end
        end
      end

      namespace :lanes do
        resource :preferred_lanes, only: %i(show update)
      end

      resource :marketing, only: :show

      namespace :overview do
        root to: 'home#show'
        resources :shippers, only: %i(index show)
      end

      resource :sponsored_content, only: :show
      resource :email_signature, only: :show do
        collection do
          get :preview
        end
      end

      resources :widgets
      resource :reviews, only: :show
      resource :subscription, only: %i(show edit)
    end

    resources :bookmarks, only: %i(new create), module: :brokerages do
      collection do
        post :create_list
      end
    end

    resource :note, only: %i(new create show destroy), module: :brokerages

    resources :brokerage_reviews, only: %i(new create show edit update), shallow: true, path: 'reviews',
                                  shallow_path: 'brokerages' do
      collection do
        match :autosave, via: %i(post patch)
      end

      resource :feature, module: :brokerage_reviews, only: %i(update destroy)
      resource :reply, module: :brokerage_reviews
      resource :report, module: :brokerage_reviews
      resource :widget, module: :brokerage_reviews, only: %i(update destroy)
    end

    resource :verification, only: %i(show create update), module: :brokerages do
      collection do
        get :verify
      end
    end

    resources :request_for_proposals, only: %i(new create), module: :brokerages
  end

  resources :personas, only: [] do
    resource :verification, only: %i(new show create), module: :personas
  end

  get '/carrier/:id', to: (redirect do |params|
    params[:id].split('-').last
      .then { |docket_number| OperatingAuthority.joins(:company).find_by!(docket_number:) }
      .then { |authority| Routes.carrier_url(authority.company) }
  end)

  resources :carriers, only: :show do
    get '/assets/:asset_id', to: 'carriers/assets#show', as: :asset

    resource :contact, only: %i(show), module: :carriers
    resources :claims, only: %i(show new create), module: :carriers
    resources :email_claims, only: %i(show update), module: :carriers, path: 'email-claims', param: :token

    namespace :dashboard, module: 'carrier_dashboard' do
      root to: 'overview/home#show'

      namespace :analytics do
        root to: 'home#show'

        resources :integrations, only: %i(index create)

        resources :feeds do
          resources :integrations, only: %i(index create edit update destroy), module: :feeds

          member do
            post :duplicate
            post :export
          end

          resources :shipper_intents, only: %i(index show) do
            collection do
              get :search
            end
          end
        end
      end

      resources :assets
      resources :driver_jobs
      resource :marketing, only: :show
      resource :sponsored_content, only: :show
      resource :email_signature, only: :show do
        collection do
          get :preview
        end
      end

      resources :availabilities, only: %i(index new create edit update destroy)
      resources :badges, only: %i(index show update)
      resources :widgets

      resource :profile, only: %i(show update) do
        resource :services, only: %i(show update), module: 'profile'
        resources :contacts, only: %i(index new create edit update destroy), module: 'profile' do
          member do
            patch :make_default
          end
        end
      end

      namespace :lanes do
        root to: 'home#show'
        resource :operation_states, only: %i(show update)
        resource :preferred_lanes, only: %i(show update)
        resource :terminals, only: %i(show update)
      end

      namespace :overview do
        root to: 'home#show'
        resources :shippers, only: %i(index show)
      end

      resource :reviews, only: :show

      resource :subscription, only: %i(show edit)

      namespace :website do
        root to: 'home#show'
        resource :styles, only: %i(show update)
        resource :sections, only: %i(show update)
        resource :settings, only: %i(show update)
        resource :socials, only: %i(show)
      end
    end

    collection do
      get '/write-review', to: 'carriers/write_review#search', as: :write_review
      get :claim
      get '/claim/:partner_id', to: 'partner_claims#show', as: :partner_claim
      get '/driver-reviews', to: 'write_driver_carrier_review#new', as: :driver_reviews
    end

    member do
      get :availability
      get :related
      get :review
    end

    resources :bookmarks, only: %i(new create), module: :carriers do
      collection do
        post :create_list
      end
    end

    resource :note, only: %i(new create show destroy), module: :carriers

    resource :driver, only: :show, module: :carriers

    resources :driver_jobs, only: %i(index show), path: 'driver-jobs', shallow: true do
      resources :driver_job_applications, only: %i(new create), path: 'driver-job-applications', shallow: true,
                                          as: :applications
    end

    resources :driver_reviews, only: %i(show new create edit update), path: 'driver-reviews', shallow: true,
                               module: :carriers do
      collection do
        match :autosave, via: %i(post patch)
      end
    end

    resources :reviews, only: %i(new create show edit update), shallow: true do
      collection do
        match :autosave, via: %i(post patch)
      end

      resource :feature, module: :reviews, only: %i(update destroy)
      resource :reply, module: :reviews
      resource :report, module: :reviews
      resource :website, module: :reviews, only: %i(update destroy)
      resource :widget, module: :reviews, only: %i(update destroy)
    end

    resource :verification, only: %i(show create update), module: :carriers do
      collection do
        get :verify
      end
    end

    resources :request_for_proposals, only: %i(new create), module: :carriers do
      member do
        post '/alternate/:alternate_carrier_id', to: 'request_for_proposals#alternate', as: :alternate
      end
    end
  end

  resource :carrier_csv_export, only: :create

  resources :cities, only: [] do
    collection do
      get :search
    end
  end

  resource :contact_sales, only: %i(new create)

  namespace :dashboard do
    root to: 'profiles#show'
    resource :profile, only: %i(show update)
    resource :reviews, only: :show
    resources :lists, only: %i(index show edit update destroy)
    resource :recently_viewed, only: :show
    resource :subscription, only: %i(create show edit) do
      collection do
        get :success
      end
    end
    resource :notifications, only: %i(show update)
    resources :bookmarks, only: %i(destroy)
  end

  resources :user_emails, only: %i(create destroy) do
    collection do
      get :verify
    end

    member do
      patch :make_primary
    end
  end

  resources :lane_searches, only: %i(index create), path: 'lane-search'
  get '/lane-search/:origin/:destination', to: 'lane_searches#show', as: :lane_search

  get 'robots.txt', to: 'robots#show'

  resources :carrier_network_builders do
    resources :lanes, controller: 'carrier_network_builder_lanes'
  end

  get '/brokerage-companies', to: 'brokerage_companies#index', as: :brokerage_companies
  get '/trucking-companies', to: 'trucking_companies/locations#index', as: :trucking_companies
  get '/trucking-companies/search', to: 'trucking_companies/search#show', as: :search_trucking_companies

  constraints Geo::Country do
    get '/brokerage-companies/:country', to: 'brokerage_companies#country', as: :country_brokerage_companies
    get '/brokerage-companies/:country/:state', to: 'brokerage_companies#state', as: :state_brokerage_companies
    get '/brokerage-companies/:country/:state/:city', to: 'brokerage_companies#city', as: :city_brokerage_companies

    get '/trucking-companies/:country', to: 'trucking_companies/locations#country', as: :country_trucking_companies
    get '/trucking-companies/:country/:state', to: 'trucking_companies/locations#state', as: :state_trucking_companies
    get '/trucking-companies/:country/:state/:city', to: 'trucking_companies/locations#city',
                                                     as: :city_trucking_companies
  end

  constraints TruckingCompanies::Load do
    get '/trucking-companies/:id', to: 'trucking_companies/loads#index', as: :load_trucking_companies
    get '/trucking-companies/:id/:country', to: 'trucking_companies/loads#country',
                                            as: :country_load_trucking_companies
    get '/trucking-companies/:id/:country/:state', to: 'trucking_companies/loads#state',
                                                   as: :state_load_trucking_companies
    get '/trucking-companies/:id/:country/:state/:city', to: 'trucking_companies/loads#city',
                                                         as: :city_load_trucking_companies
  end

  post '/webhooks/:source', to: 'webhooks#create'

  resources :passwords, controller: 'clearance/passwords', only: %i(create new)
  resource :session, controller: 'sessions', only: [:create]

  resources :users, controller: 'users', only: %i(create update) do
    resources :personas, only: %i(new create), module: :users
    resource :password,
             controller: 'clearance/passwords',
             only: %i(edit update)

    resource :verification, only: %i(show create update), module: :users do
      collection do
        get :verify
      end
    end

    resources :profiles, only: %i(index), module: :users
  end

  resources :widgets, only: %i(show) do
    member do
      get :embed_html
      get :image
    end
  end
  resources :wizards, only: %i(show)

  get '/login' => 'sessions#new', as: :sign_in
  get '/signup' => 'users#new', as: :sign_up

  constraints Clearance::Constraints::SignedIn.new do
    delete '/logout' => 'clearance/sessions#destroy', as: :sign_out
  end

  resource :protected_feature, only: :show
  resource :pricing, only: :show do
    collection do
      get :carrier
      get :brokerage
      get :broker
      get :dispatcher
    end
  end
end
