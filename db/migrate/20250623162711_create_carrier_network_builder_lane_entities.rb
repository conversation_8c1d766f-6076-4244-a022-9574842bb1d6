class CreateCarrierNetworkBuilderLaneEntities < ActiveRecord::Migration[8.0]
  def change
    create_table :carrier_network_builder_lane_entities do |t|
      t.references :carrier_network_builder_lane, null: false, foreign_key: { on_delete: :cascade }, index: false
      t.references :company, null: false, foreign_key: false
      t.string :entity_type, null: false
      t.integer :row_order

      t.timestamps
    end

    add_index :carrier_network_builder_lane_entities, %i(carrier_network_builder_lane_id company_id entity_type),
              name: 'index_carrier_network_builder_lane_entities_on_lane_and_entity', unique: true
  end
end
