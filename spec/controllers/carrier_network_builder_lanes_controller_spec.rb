require 'rails_helper'

RSpec.describe CarrierNetworkBuilderLanesController, type: :controller do
  let(:user) { create(:user) }
  let(:carrier_network_builder) { create(:carrier_network_builder, user: user) }
  let(:lane) { create(:carrier_network_builder_lane, carrier_network_builder: carrier_network_builder) }
  let(:valid_attributes) do
    {
      origin_id: 'united-states:illinois:chicago',
      destination_id: 'united-states:california:los-angeles',
      volume: '10-15 loads/week',
      frequency: 'Weekly',
      notes: 'Test lane'
    }
  end
  let(:invalid_attributes) { { origin_id: '', destination_id: '' } }

  before { sign_in_as(user) }

  describe 'GET #index' do
    it 'returns a success response' do
      get :index, params: { carrier_network_builder_id: carrier_network_builder.to_param }
      expect(response).to be_successful
    end

    it 'assigns the carrier network builder lanes' do
      lane # create the record
      get :index, params: { carrier_network_builder_id: carrier_network_builder.to_param }
      expect(assigns(:lanes)).to eq([lane])
    end
  end

  describe 'GET #show' do
    it 'returns a success response' do
      get :show, params: { carrier_network_builder_id: carrier_network_builder.to_param, id: lane.to_param }
      expect(response).to be_successful
    end
  end

  describe 'GET #new' do
    it 'returns a success response' do
      get :new, params: { carrier_network_builder_id: carrier_network_builder.to_param }
      expect(response).to be_successful
    end
  end

  describe 'GET #edit' do
    it 'returns a success response' do
      get :edit, params: { carrier_network_builder_id: carrier_network_builder.to_param, id: lane.to_param }
      expect(response).to be_successful
    end
  end

  describe 'POST #create' do
    context 'with valid params' do
      it 'creates a new CarrierNetworkBuilderLane' do
        expect {
          post :create, params: {
            carrier_network_builder_id: carrier_network_builder.to_param,
            carrier_network_builder_lane: valid_attributes
          }
        }.to change(CarrierNetworkBuilderLane, :count).by(1)
      end

      it 'redirects to the lanes index' do
        post :create, params: {
          carrier_network_builder_id: carrier_network_builder.to_param,
          carrier_network_builder_lane: valid_attributes
        }
        expect(response).to redirect_to(carrier_network_builder_lanes_path(carrier_network_builder))
      end
    end

    context 'with invalid params' do
      it 'returns a success response (i.e. to display the \'new\' template)' do
        post :create, params: {
          carrier_network_builder_id: carrier_network_builder.to_param,
          carrier_network_builder_lane: invalid_attributes
        }
        expect(response).to have_http_status(:unprocessable_entity)
      end
    end
  end

  describe 'PUT #update' do
    context 'with valid params' do
      let(:new_attributes) { { volume: '20-25 loads/week' } }

      it 'updates the requested lane' do
        put :update, params: {
          carrier_network_builder_id: carrier_network_builder.to_param,
          id: lane.to_param,
          carrier_network_builder_lane: new_attributes
        }
        lane.reload
        expect(lane.volume).to eq('20-25 loads/week')
      end

      it 'redirects to the lanes index' do
        put :update, params: {
          carrier_network_builder_id: carrier_network_builder.to_param,
          id: lane.to_param,
          carrier_network_builder_lane: new_attributes
        }
        expect(response).to redirect_to(carrier_network_builder_lanes_path(carrier_network_builder))
      end
    end

    context 'with invalid params' do
      it 'returns a success response (i.e. to display the \'edit\' template)' do
        put :update, params: {
          carrier_network_builder_id: carrier_network_builder.to_param,
          id: lane.to_param,
          carrier_network_builder_lane: invalid_attributes
        }
        expect(response).to have_http_status(:unprocessable_entity)
      end
    end
  end

  describe 'DELETE #destroy' do
    it 'destroys the requested lane' do
      lane # create the record
      expect {
        delete :destroy, params: {
          carrier_network_builder_id: carrier_network_builder.to_param,
          id: lane.to_param
        }
      }.to change(CarrierNetworkBuilderLane, :count).by(-1)
    end

    it 'redirects to the lanes index' do
      delete :destroy, params: {
        carrier_network_builder_id: carrier_network_builder.to_param,
        id: lane.to_param
      }
      expect(response).to redirect_to(carrier_network_builder_lanes_path(carrier_network_builder))
    end
  end

  context 'when user is not signed in' do
    before { sign_out }

    it 'redirects to sign in page' do
      get :index, params: { carrier_network_builder_id: carrier_network_builder.to_param }
      expect(response).to redirect_to(sign_in_url)
    end
  end

  context 'when accessing another user\'s network builder' do
    let(:other_user) { create(:user) }
    let(:other_builder) { create(:carrier_network_builder, user: other_user) }

    it 'raises ActiveRecord::RecordNotFound' do
      expect {
        get :index, params: { carrier_network_builder_id: other_builder.to_param }
      }.to raise_error(ActiveRecord::RecordNotFound)
    end
  end
end
