require 'rails_helper'

RSpec.describe CarrierNetworkBuilderLanes::CreateFromChatbotResponse do
  let(:user) { create(:user) }
  let(:carrier_network_builder) { create(:carrier_network_builder, user: user) }

  let(:chatbot_response) do
    {
      type: 'requirements',
      data: {
        origin: 'Chicago, IL',
        destination: 'Los Angeles, CA',
        origin_location_id: 'united-states:illinois:chicago',
        destination_location_id: 'united-states:california:los-angeles',
        volume: '10-15 loads/week',
        frequency: 'Weekly',
        truck_type: [1, 2],
        shipment_type: [1],
        freight: [3, 4]
      },
      elasticsearch_query: {
        filters: {
          truck_type_ids: [1, 2],
          shipment_type_ids: [1],
          freight_ids: [3, 4]
        },
        shoulds: {},
        order: [:cs_score]
      }
    }
  end

  subject(:service) do
    described_class.new(
      carrier_network_builder: carrier_network_builder,
      chatbot_response: chatbot_response
    )
  end

  describe '#call' do
    it 'creates a new CarrierNetworkBuilderLane' do
      expect { service.call }.to change(CarrierNetworkBuilderLane, :count).by(1)
    end

    it 'sets the correct attributes' do
      lane = service.call

      expect(lane.carrier_network_builder).to eq(carrier_network_builder)
      expect(lane.origin_id).to eq('united-states:illinois:chicago')
      expect(lane.destination_id).to eq('united-states:california:los-angeles')
      expect(lane.volume).to eq('10-15 loads/week')
      expect(lane.frequency).to eq('Weekly')
    end

    it 'infers types from the IDs' do
      # Mock the LaneSearches::Location to return expected types
      origin_location = instance_double(LaneSearches::Location, type: :city)
      destination_location = instance_double(LaneSearches::Location, type: :city)

      allow(LaneSearches::Location).to receive(:new).with('united-states:illinois:chicago').and_return(origin_location)
      allow(LaneSearches::Location).to receive(:new).with('united-states:california:los-angeles').and_return(destination_location)

      lane = service.call

      expect(lane.origin_type).to eq('city')
      expect(lane.destination_type).to eq('city')
    end

    it 'converts elasticsearch filters to form filters and includes origin/destination' do
      lane = service.call

      expect(lane.filters).to eq({
                                   'truck_type' => [1, 2],
                                   'shipment_type' => [1],
                                   'freight' => [3, 4],
                                   'origin' => 'united-states:illinois:chicago',
                                   'destination' => 'united-states:california:los-angeles'
                                 })
    end

    context 'when chatbot response has additional filters' do
      before do
        chatbot_response[:elasticsearch_query][:filters].merge!({
                                                                  'authorized' => true,
                                                                  'star_rating' => { 'min' => 3, 'max' => 5 }
                                                                })
      end

      it 'includes other filters as-is along with origin/destination' do
        lane = service.call

        expect(lane.filters).to include({
                                          'authorized' => true,
                                          'star_rating' => { 'min' => 3, 'max' => 5 },
                                          'origin' => 'united-states:illinois:chicago',
                                          'destination' => 'united-states:california:los-angeles'
                                        })
      end
    end

    context 'when volume and frequency are missing' do
      before do
        chatbot_response[:data].delete(:volume)
        chatbot_response[:data].delete(:frequency)
      end

      it 'creates the lane with nil values' do
        lane = service.call

        expect(lane.volume).to be_nil
        expect(lane.frequency).to be_nil
      end
    end

    context 'when filters are empty' do
      before do
        chatbot_response[:elasticsearch_query][:filters] = {}
      end

      it 'creates the lane with only origin and destination in filters' do
        lane = service.call

        expect(lane.filters).to eq({
                                     'origin' => 'united-states:illinois:chicago',
                                     'destination' => 'united-states:california:los-angeles'
                                   })
      end
    end
  end

  describe 'private methods' do
    describe '#extract_filters' do
      it 'maps elasticsearch filter keys to form field names and includes origin/destination' do
        filters = service.send(:extract_filters)

        expect(filters).to eq({
                                'truck_type' => [1, 2],
                                'shipment_type' => [1],
                                'freight' => [3, 4],
                                'origin' => 'united-states:illinois:chicago',
                                'destination' => 'united-states:california:los-angeles'
                              })
      end
    end


  end
end
