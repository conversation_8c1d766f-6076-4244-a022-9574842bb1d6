require 'rails_helper'

RSpec.describe LaneSearches::Location do
  subject(:location) { described_class.new(id) }

  describe '#type' do
    context 'when id is a city' do
      let(:id) { 'united-states:utah:salt-lake-city' }

      it 'returns :city' do
        expect(location.type).to eq(:city)
      end
    end

    context 'when id is a state' do
      let(:id) { 'united-states:utah' }

      it 'returns :state' do
        expect(location.type).to eq(:state)
      end
    end

    context 'when id is a region' do
      let(:id) { 'united-states:west' }

      it 'returns :region' do
        expect(location.type).to eq(:region)
      end
    end

    context 'when id format is invalid' do
      let(:id) { 'invalid_format' }

      it 'raises an ArgumentError' do
        expect { location.type }.to raise_error(ArgumentError, "Invalid ID format: #{id}")
      end
    end
  end

  describe '#record' do
    context 'when id is a city' do
      let(:id) { 'united-states:utah:salt-lake-city' }
      let!(:city) { create :city, name: 'Salt Lake City', state_code: 'UT' }

      it 'returns the city record' do
        expect(location.record).to eq city
      end
    end

    context 'when id is a state' do
      let(:id) { 'united-states:utah' }

      it 'returns the state record' do
        expect(location.record).to eq Geo::State.find('united-states:utah')
      end
    end

    context 'when id is a region' do
      let(:id) { 'united-states:west' }

      it 'returns the region record' do
        expect(location.record).to eq Geo::Region.find('united-states:west')
      end
    end
  end
end
