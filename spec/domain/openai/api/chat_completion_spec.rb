require 'rails_helper'

RSpec.describe OpenAI::Api::ChatCompletion do
  subject(:chat_completion) { described_class.new(raise_errors: true) }

  let(:messages) do
    [
      { role: 'system', content: 'You are a helpful assistant.' },
      { role: 'user', content: 'Hello!' }
    ]
  end

  describe '#create' do
    let(:response_body) do
      {
        id: 'chatcmpl-123',
        object: 'chat.completion',
        created: 1677652288,
        model: 'gpt-4o-mini',
        choices: [
          {
            index: 0,
            message: {
              role: 'assistant',
              content: 'Hello! How can I help you today?'
            },
            finish_reason: 'stop'
          }
        ],
        usage: {
          prompt_tokens: 9,
          completion_tokens: 12,
          total_tokens: 21
        }
      }.to_json
    end

    context 'when request succeeds' do
      let!(:stub) do
        stub_request(:post, 'https://api.openai.com/v1/chat/completions')
          .with(
            body: {
              model: 'gpt-4o-mini',
              messages: messages,
              temperature: 0.7,
              max_tokens: 1000
            }.to_json
          )
          .and_return(status: 200, body: response_body, headers: { 'Content-Type' => 'application/json' })
      end

      it 'creates chat completion' do
        response = chat_completion.create(messages: messages)
        expect(response.status).to eq(200)
        expect(stub).to have_been_requested
      end
    end

    context 'when request fails' do
      let!(:stub) do
        stub_request(:post, 'https://api.openai.com/v1/chat/completions')
          .and_return(status: 500)
      end

      it 'raises error' do
        expect { chat_completion.create(messages: messages) }.to raise_error OpenAI::Api::Error
        expect(stub).to have_been_requested
      end
    end

    context 'with custom parameters' do
      let!(:stub) do
        stub_request(:post, 'https://api.openai.com/v1/chat/completions')
          .with(
            body: {
              model: 'gpt-4',
              messages: messages,
              temperature: 0.5,
              max_tokens: 500,
              response_format: { type: 'json_object' }
            }.to_json
          )
          .and_return(status: 200, body: response_body, headers: { 'Content-Type' => 'application/json' })
      end

      it 'uses custom parameters' do
        chat_completion.create(
          messages: messages,
          model: 'gpt-4',
          temperature: 0.5,
          max_tokens: 500,
          response_format: { type: 'json_object' }
        )
        expect(stub).to have_been_requested
      end
    end
  end
end
