FactoryBot.define do
  factory :carrier_network_builder_lane do
    association :carrier_network_builder
    origin_type { %w[city region state].sample }
    origin_id { Faker::Number.number(digits: 3).to_s }
    destination_type { %w[city region state].sample }
    destination_id { Faker::Number.number(digits: 3).to_s }
    volume { "#{Faker::Number.between(from: 5, to: 50)} loads/week" }
    frequency { %w[Daily Weekly Monthly].sample }
    notes { Faker::Lorem.sentence }
    filters { { equipment_type: %w[van reefer flatbed].sample } }
  end
end
