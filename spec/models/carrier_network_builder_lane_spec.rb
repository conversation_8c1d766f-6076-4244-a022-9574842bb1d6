require 'rails_helper'

RSpec.describe CarrierNetworkBuilderLane do
  describe '#origin' do
    context 'when origin_id is blank' do
      let(:lane) { build :carrier_network_builder_lane, origin_id: nil }

      it 'returns a nullable city object' do
        expect(lane.origin).to be_a(Nullable::City)
      end
    end

    context 'when origin_id is present' do
      let(:lane) { build :carrier_network_builder_lane, origin_id: city.full_slug }
      let(:city) { create :city, name: 'Chicago', state_code: 'IL' }

      it 'finds the origin city' do
        expect(lane.origin).to eq(city)
      end
    end
  end

  describe '#destination' do
    context 'when destination_id is blank' do
      let(:lane) { build :carrier_network_builder_lane, destination_id: nil }

      it 'returns a nullable city object' do
        expect(lane.destination).to be_a(Nullable::City)
      end
    end

    context 'when destination_id is present' do
      let(:lane) { build :carrier_network_builder_lane, destination_id: city.full_slug }
      let(:city) { create :city, name: 'Los Angeles', state_code: 'CA' }

      it 'finds the destination city' do
        expect(lane.destination).to eq(city)
      end
    end
  end
end
