require 'rails_helper'

RSpec.describe 'CarrierNetworkBuilderLanes', type: :request do
  let(:user) { create(:user) }
  let(:carrier_network_builder) { create(:carrier_network_builder, user: user) }
  let(:lane) { create(:carrier_network_builder_lane, carrier_network_builder: carrier_network_builder) }

  before { sign_in_as(user) }

  describe 'GET /carrier_network_builders/:carrier_network_builder_id/lanes' do
    it 'renders the lanes index page' do
      get carrier_network_builder_lanes_path(carrier_network_builder)
      expect(response).to have_http_status(:success)
      expect(response.body).to include('Lanes')
    end
  end

  describe 'GET /carrier_network_builders/:carrier_network_builder_id/lanes/:id' do
    it 'renders the lane show page' do
      get carrier_network_builder_lane_path(carrier_network_builder, lane)
      expect(response).to have_http_status(:success)
      expect(response.body).to include('Lane Details')
    end
  end

  describe 'GET /carrier_network_builders/:carrier_network_builder_id/lanes/new' do
    it 'renders the new lane page' do
      get new_carrier_network_builder_lane_path(carrier_network_builder)
      expect(response).to have_http_status(:success)
      expect(response.body).to include('Add New Lane')
    end
  end

  describe 'POST /carrier_network_builders/:carrier_network_builder_id/lanes' do
    let(:valid_params) do
      {
        carrier_network_builder_lane: {
          origin_id: 'united-states:illinois:chicago',
          destination_id: 'united-states:california:los-angeles',
          volume: '10-15 loads/week',
          frequency: 'Weekly'
        }
      }
    end

    it 'creates a new lane' do
      expect {
        post carrier_network_builder_lanes_path(carrier_network_builder), params: valid_params
      }.to change(CarrierNetworkBuilderLane, :count).by(1)

      expect(response).to redirect_to(carrier_network_builder_lanes_path(carrier_network_builder))
    end
  end

  describe 'GET /carrier_network_builders/:carrier_network_builder_id/lanes/:id/edit' do
    it 'renders the edit lane page' do
      get edit_carrier_network_builder_lane_path(carrier_network_builder, lane)
      expect(response).to have_http_status(:success)
      expect(response.body).to include('Edit Lane')
    end
  end

  describe 'PATCH /carrier_network_builders/:carrier_network_builder_id/lanes/:id' do
    let(:update_params) do
      {
        carrier_network_builder_lane: {
          volume: '20-25 loads/week'
        }
      }
    end

    it 'updates the lane' do
      patch carrier_network_builder_lane_path(carrier_network_builder, lane), params: update_params
      expect(response).to redirect_to(carrier_network_builder_lanes_path(carrier_network_builder))
      expect(lane.reload.volume).to eq('20-25 loads/week')
    end
  end

  describe 'DELETE /carrier_network_builders/:carrier_network_builder_id/lanes/:id' do
    it 'deletes the lane' do
      lane # create the record
      expect {
        delete carrier_network_builder_lane_path(carrier_network_builder, lane)
      }.to change(CarrierNetworkBuilderLane, :count).by(-1)

      expect(response).to redirect_to(carrier_network_builder_lanes_path(carrier_network_builder))
    end
  end

  context 'when not signed in' do
    before { sign_out }

    it 'redirects to sign in page' do
      get carrier_network_builder_lanes_path(carrier_network_builder)
      expect(response).to redirect_to(sign_in_url)
    end
  end

  context 'when accessing another user\'s network builder' do
    let(:other_user) { create(:user) }
    let(:other_builder) { create(:carrier_network_builder, user: other_user) }

    it 'raises ActiveRecord::RecordNotFound' do
      expect {
        get carrier_network_builder_lanes_path(other_builder)
      }.to raise_error(ActiveRecord::RecordNotFound)
    end
  end
end
