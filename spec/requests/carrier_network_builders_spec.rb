require 'rails_helper'

RSpec.describe 'CarrierNetworkBuilders', type: :request do
  let(:user) { create(:user) }
  let(:carrier_network_builder) { create(:carrier_network_builder, user: user) }

  before { sign_in_as(user) }

  describe 'GET /carrier_network_builders' do
    it 'renders the index page' do
      get carrier_network_builders_path
      expect(response).to have_http_status(:success)
      expect(response.body).to include('Carrier Network Builders')
    end
  end

  describe 'GET /carrier_network_builders/:id' do
    it 'renders the show page' do
      get carrier_network_builder_path(carrier_network_builder)
      expect(response).to have_http_status(:success)
      expect(response.body).to include(carrier_network_builder.name)
    end
  end

  describe 'GET /carrier_network_builders/new' do
    it 'renders the new page' do
      get new_carrier_network_builder_path
      expect(response).to have_http_status(:success)
      expect(response.body).to include('New Carrier Network Builder')
    end
  end

  describe 'POST /carrier_network_builders' do
    let(:valid_params) { { carrier_network_builder: { name: 'Test Network' } } }

    it 'creates a new carrier network builder' do
      expect {
        post carrier_network_builders_path, params: valid_params
      }.to change(CarrierNetworkBuilder, :count).by(1)

      expect(response).to redirect_to(CarrierNetworkBuilder.last)
    end
  end

  describe 'GET /carrier_network_builders/:id/edit' do
    it 'renders the edit page' do
      get edit_carrier_network_builder_path(carrier_network_builder)
      expect(response).to have_http_status(:success)
      expect(response.body).to include('Edit Network Builder')
    end
  end

  describe 'PATCH /carrier_network_builders/:id' do
    let(:update_params) { { carrier_network_builder: { name: 'Updated Network' } } }

    it 'updates the carrier network builder' do
      patch carrier_network_builder_path(carrier_network_builder), params: update_params
      expect(response).to redirect_to(carrier_network_builder)
      expect(carrier_network_builder.reload.name).to eq('Updated Network')
    end
  end

  describe 'DELETE /carrier_network_builders/:id' do
    it 'deletes the carrier network builder' do
      carrier_network_builder # create the record
      expect {
        delete carrier_network_builder_path(carrier_network_builder)
      }.to change(CarrierNetworkBuilder, :count).by(-1)

      expect(response).to redirect_to(carrier_network_builders_path)
    end
  end

  context 'when not signed in' do
    before { sign_out }

    it 'redirects to sign in page' do
      get carrier_network_builders_path
      expect(response).to redirect_to(sign_in_url)
    end
  end
end
