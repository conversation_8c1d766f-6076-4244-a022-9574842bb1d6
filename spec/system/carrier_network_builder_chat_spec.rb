require 'rails_helper'

RSpec.describe 'Carrier Network Builder Chat', type: :system, js: true do
  let(:user) { create(:user) }
  let(:carrier_network_builder) { create(:carrier_network_builder, user: user) }

  before do
    sign_in user
    
    # Mock the AI chatbot response
    allow(CarrierNetworkBuilders::ShipperProspectingChatbot).to receive(:new).and_return(
      double(call: {
        type: 'message',
        content: 'Hello! I\'d be happy to help you find carriers and brokerages. Where will you be shipping from?'
      })
    )
  end

  it 'allows users to chat with the AI assistant' do
    visit carrier_network_builder_lanes_path(carrier_network_builder)

    # Check that the chat interface is present
    expect(page).to have_text('Hi, I am your shipping assistant')
    expect(page).to have_field('Type your message...')

    # Send a message
    fill_in 'Type your message...', with: 'I need to ship from Chicago to Los Angeles'
    click_button type: 'submit'

    # Check that the user message appears
    expect(page).to have_text('I need to ship from Chicago to Los Angeles')

    # Check that the AI response appears
    expect(page).to have_text('Hello! I\'d be happy to help you find carriers and brokerages')
  end

  it 'handles AI chatbot errors gracefully' do
    # Mock an error response
    allow(CarrierNetworkBuilders::ShipperProspectingChatbot).to receive(:new).and_return(
      double(call: -> { raise StandardError, 'API Error' })
    )

    visit carrier_network_builder_lanes_path(carrier_network_builder)

    # Send a message
    fill_in 'Type your message...', with: 'Test message'
    click_button type: 'submit'

    # Check that error message appears
    expect(page).to have_text('I apologize, but I\'m having trouble processing your request')
  end

  it 'shows typing indicator while processing' do
    # Mock a delayed response
    allow(CarrierNetworkBuilders::ShipperProspectingChatbot).to receive(:new).and_return(
      double(call: -> { 
        sleep(0.1) # Small delay to see typing indicator
        {
          type: 'message',
          content: 'Response after delay'
        }
      })
    )

    visit carrier_network_builder_lanes_path(carrier_network_builder)

    # Send a message
    fill_in 'Type your message...', with: 'Test message'
    click_button type: 'submit'

    # Check that typing indicator appears briefly
    expect(page).to have_text('Typing...', wait: 1)
    
    # Check that response appears
    expect(page).to have_text('Response after delay')
  end

  it 'handles requirements collection' do
    # Mock a requirements response
    allow(CarrierNetworkBuilders::ShipperProspectingChatbot).to receive(:new).and_return(
      double(call: {
        type: 'requirements',
        data: {
          origin: 'Chicago, IL',
          destination: 'Los Angeles, CA',
          truck_type: [1],
          volume: '10 pallets'
        },
        elasticsearch_query: {
          filters: { truck_type_ids: [1] }
        }
      })
    )

    visit carrier_network_builder_lanes_path(carrier_network_builder)

    # Send a message
    fill_in 'Type your message...', with: 'I need dry van from Chicago to LA, 10 pallets'
    click_button type: 'submit'

    # Check that success message appears
    expect(page).to have_text('Great! I found your shipping requirements')
  end
end
